<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地质灾害风险查询 - 茂名市自然资源局</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 天地图API -->
    <script type="text/javascript" src="http://api.tianditu.gov.cn/api?v=4.0&tk=7b0f309efd0864b4ce49fafda0302277"></script>
    <!-- 模拟数据 -->
    <script type="text/javascript" src="../mock-data.js"></script>
    <style>
        /* 优化后的政府网站风格 - 美观、专业、可信、规范 */
        body {
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .query-container {
            display: flex;
            height: calc(100vh - 124px); /* 减去navbar(64px)和footer(60px)的高度 */
            max-height: calc(100vh - 124px);
            overflow: hidden;
        }

        .query-sidebar {
            width: 380px;
            background-color: #DBEAFE; /* 浅蓝色背景，与navbar形成区分 */
            border-right: 1px solid #93C5FD;
            padding: 20px;
            overflow-x: hidden; /* 隐藏横向滚动条 */
            box-shadow: 2px 0 8px rgba(30, 64, 175, 0.1);
            position: relative;
            transition: width 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .sidebar-section {
            margin-bottom: 20px;
            padding: 16px;
            background-color: #FFFFFF;
            border: 1px solid #93C5FD;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(30, 64, 175, 0.05);
        }

        .sidebar-section.legend-section {
            flex-shrink: 0; /* 不允许收缩 */
            margin-bottom: 0; /* 最后一个区域不需要下边距 */
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: #1E40AF;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3B82F6;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar-title i {
            color: #3B82F6;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
            background-color: #ffffff;
            color: #1f2937;
        }

        .form-select:focus {
            outline: none;
            border-color: #1e40af;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .btn-locate {
            width: 100%;
            padding: 12px 16px;
            background-color: #1e40af;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 16px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-locate:hover {
            background-color: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(30, 64, 175, 0.2);
        }



        .map-container {
            flex: 1;
            position: relative;
            background-color: #ffffff;
        }

        #mapDiv {
            width: 100%;
            height: 100%;
        }

        /* 地图工具按钮样式 - 图标和文字在按钮内部 */
        .map-tools {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        /* 图例按钮需要相对定位以作为面板的参考 */
        #legendBtn {
            position: relative;
        }

        .tool-button {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            color: #666666;
            font-size: 10px;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            padding: 6px;
        }

        .tool-button i {
            font-size: 16px;
        }

        .tool-button span {
            font-weight: 500;
            white-space: nowrap;
            text-align: center;
            line-height: 1;
        }

        .tool-button:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .tool-button.active {
            background: rgba(30, 64, 175, 0.9);
            color: #FFFFFF;
            border-color: #1E40AF;
        }

        .tool-button.active:hover {
            background: rgba(30, 64, 175, 1);
            transform: translateY(-2px);
        }

        .sidebar-legend {
            margin-top: 8px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
            color: #374151;
        }

        .legend-color {
            width: 14px;
            height: 14px;
            border-radius: 3px;
            margin-right: 10px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .legend-point {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px currentColor;
        }

        .legend-high { background-color: #ef4444; }
        .legend-medium { background-color: #f59e0b; }
        .legend-low { background-color: #10b981; }
        .legend-disaster { color: #3B82F6; }

        /* 预警信息样式 */
        .warning-section {
            display: flex;
            flex-direction: column;
            flex: 1; /* 占用剩余空间 */
            min-height: 0; /* 允许收缩 */
            border-bottom: 1px solid #E5E7EB;
            margin-bottom: 16px;
        }

        .warning-list {
            flex: 1;
            overflow-y: auto;
            margin: 16px 0;
            min-height: 0; /* 允许收缩 */
        }

        .warning-item {
            background-color: #FFFFFF;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .warning-item:hover {
            border-color: #3B82F6;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        }

        .warning-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .warning-level {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .warning-level-1 { background-color: #EF4444; } /* 红色 */
        .warning-level-2 { background-color: #F59E0B; } /* 橙色 */
        .warning-level-3 { background-color: #EAB308; } /* 黄色 */
        .warning-level-4 { background-color: #3B82F6; } /* 蓝色 */

        .warning-title {
            font-size: 13px;
            font-weight: 500;
            color: #111827;
            flex: 1;
            line-height: 1.3;
        }

        .warning-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: #6B7280;
        }

        .warning-time {
            font-size: 11px;
        }

        .warning-areas {
            font-size: 11px;
        }

        .warning-towns {
            margin-top: 8px;
            font-size: 11px;
            color: #6B7280;
            line-height: 1.4;
        }

        .warning-towns-label {
            font-weight: 500;
            color: #374151;
        }

        /* 预警状态标识样式 */
        .warning-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 500;
            margin-left: 8px;
            line-height: 1;
        }

        .warning-status.active {
            background-color: #10B981;
            color: #FFFFFF;
        }

        .warning-status.inactive {
            background-color: #6B7280;
            color: #FFFFFF;
        }

        /* 地图浮动预警信息区域 - 警铃+信息条 */
        .map-warning-banner {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1001;
            display: none; /* 默认隐藏 */
            flex-direction: column; /* 改为垂直布局以支持展开面板 */
            align-items: center;
            gap: 10px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .map-warning-banner.show {
            display: flex;
        }

        /* 信息条主体容器 */
        .warning-banner-main {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 独立警铃图标 - 透明背景 + 白色光晕 + 摇摆动画 */
        .warning-banner-icon {
            font-size: 24px;
            flex-shrink: 0;
            filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8))
                    drop-shadow(0 0 8px rgba(255, 255, 255, 0.6))
                    drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
            animation: bell-swing 2s ease-in-out infinite;
            transform-origin: top center;
        }

        /* 警铃摇摆动画 */
        @keyframes bell-swing {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(3deg);
            }
            75% {
                transform: rotate(-3deg);
            }
        }

        /* 信息条容器 */
        .warning-banner-content {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            height: 32px;
            max-width: 480px;
            min-width: 280px;
            display: flex;
            align-items: center;
            overflow: hidden;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: border-radius 0.3s ease; /* 添加圆角过渡动画 */
        }

        /* 当详细面板展开时，信息条下方圆角变为直角 */
        .warning-banner-content.panel-expanded {
            border-radius: 16px 16px 0 0;
        }

        /* 固定部分 - 预警等级 */
        .warning-banner-fixed {
            display: flex;
            align-items: center;
            padding: 0 12px;
            flex-shrink: 0;
            font-size: 13px;
            font-weight: 500;
            gap: 6px;
            color: #333333;
        }

        /* 警铃图标颜色 - 根据预警等级 + 增强光晕 + 强化差异化动画 */
        .warning-banner-icon.level-1 {
            color: #FF0000; /* 红色 */
            filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.9))
                    drop-shadow(0 0 8px rgba(255, 255, 255, 0.7))
                    drop-shadow(0 0 12px rgba(255, 0, 0, 0.3));
            animation: bell-swing-critical 0.5s ease-in-out infinite;
        }

        .warning-banner-icon.level-2 {
            color: #FF8C00; /* 橙色 */
            filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.9))
                    drop-shadow(0 0 8px rgba(255, 255, 255, 0.7))
                    drop-shadow(0 0 12px rgba(255, 140, 0, 0.3));
            animation: bell-swing-urgent 0.5s ease-in-out infinite;
        }

        .warning-banner-icon.level-3 {
            color: #FFD700; /* 黄色 */
            filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.9))
                    drop-shadow(0 0 8px rgba(255, 255, 255, 0.7))
                    drop-shadow(0 0 12px rgba(255, 215, 0, 0.3));
            animation: bell-swing-moderate 0.5s ease-in-out infinite;
        }

        .warning-banner-icon.level-4 {
            color: #1E90FF; /* 蓝色 */
            filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.9))
                    drop-shadow(0 0 8px rgba(255, 255, 255, 0.7))
                    drop-shadow(0 0 12px rgba(30, 144, 255, 0.3));
            animation: bell-swing 0.5s ease-in-out infinite;
        }

        /* 一级预警（红色）- 最紧急的剧烈摇摆 */
        @keyframes bell-swing-critical {
            0%, 100% {
                transform: rotate(0deg);
            }
            12.5% {
                transform: rotate(8deg);
            }
            37.5% {
                transform: rotate(-8deg);
            }
            62.5% {
                transform: rotate(6deg);
            }
            87.5% {
                transform: rotate(-6deg);
            }
        }

        /* 二级预警（橙色）- 紧急摇摆 */
        @keyframes bell-swing-urgent {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(6deg);
            }
            75% {
                transform: rotate(-6deg);
            }
        }

        /* 三级预警（黄色）- 中等摇摆 */
        @keyframes bell-swing-moderate {
            0%, 100% {
                transform: rotate(0deg);
            }
            33% {
                transform: rotate(4deg);
            }
            67% {
                transform: rotate(-4deg);
            }
        }

        .warning-banner-icon.no-warning {
            color: #999999; /* 灰色 */
            filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8))
                    drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
            animation: none; /* 无预警时不使用动画 */
        }

        /* 滚动部分 - 镇街信息 */
        .warning-banner-scrolling-wrapper {
            flex: 1;
            overflow: hidden;
            height: 32px;
            display: flex;
            align-items: center;
            border-left: 1px solid rgba(0, 0, 0, 0.1);
            padding-left: 12px;
            padding-right: 8px; /* 减少右边距为展开按钮留空间 */
        }

        /* 展开/收起按钮 */
        .warning-expand-btn {
            background: none;
            border: none;
            padding: 6px;
            cursor: pointer;
            color: #666666;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s ease;
            flex-shrink: 0;
            width: 24px;
            height: 24px;
        }

        .warning-expand-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: #333333;
        }

        .warning-expand-btn.expanded {
            transform: rotate(180deg);
        }

        /* 滚动文本样式 */
        .warning-banner-scrolling {
            white-space: nowrap;
            overflow: hidden;
            position: relative;
            width: 100%;
        }

        .warning-banner-text {
            display: inline-block;
            animation: scroll-left 20s linear infinite;
            padding-right: 50px; /* 确保滚动时有间隔 */
            font-size: 12px;
            color: #666666;
            font-weight: 400;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(100%);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        /* 如果内容较短，不需要滚动 */
        .warning-banner-scrolling.no-scroll .warning-banner-text {
            animation: none;
            transform: none;
            padding-right: 0;
        }

        /* 预警等级标签 - 简洁设计 */
        .warning-level-label {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            margin-right: 4px;
            color: #FFFFFF;
        }

        .warning-level-label.level-1 {
            background-color: #FF4444; /* 红色 */
        }

        .warning-level-label.level-2 {
            background-color: #FF9900; /* 橙色 */
        }

        .warning-level-label.level-3 {
            background-color: #FFD700; /* 黄色 */
            color: #333333;
        }

        .warning-level-label.level-4 {
            background-color: #4A90E2; /* 蓝色 */
        }

        /* 预警详细内容面板 */
        .warning-detail-panel {
            position: absolute;
            top: 32px; /* 紧贴信息条下方 */
            left: 31px; /* 与信息条左对齐（考虑警铃图标宽度） */
            width: 480px; /* 与信息条宽度保持一致 */
            height: 0; /* 初始高度为0 */
            overflow: hidden;
            background-color: rgba(255, 255, 255, 0.9); /* 与信息条背景色保持一致 */
            border-radius: 0 0 16px 16px; /* 只有下方圆角 */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(8px); /* 与信息条模糊效果保持一致 */
            border: 1px solid rgba(255, 255, 255, 0.5); /* 与信息条边框保持一致 */
            border-top: none; /* 移除顶部边框 */
            transition: height 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
            z-index: 1000;
            opacity: 0; /* 初始透明 */
            visibility: hidden; /* 初始隐藏 */
        }

        .warning-detail-panel.expanded {
            height: 300px; /* 设置固定高度 */
            padding: 16px;
            opacity: 1; /* 展开时显示 */
            visibility: visible; /* 展开时可见 */
        }

        .warning-detail-content {
            height: 100%; /* 占满父容器 */
            display: flex;
            flex-direction: column;
        }

        /* 自定义滚动条样式 */
        .warning-detail-body::-webkit-scrollbar {
            width: 6px;
        }

        .warning-detail-body::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        .warning-detail-body::-webkit-scrollbar-thumb {
            background: rgba(30, 64, 175, 0.3);
            border-radius: 3px;
        }

        .warning-detail-body::-webkit-scrollbar-thumb:hover {
            background: rgba(30, 64, 175, 0.5);
        }

        .warning-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .warning-detail-title {
            font-size: 16px;
            font-weight: 600;
            color: #1E40AF;
            margin: 0;
        }

        .warning-detail-time {
            font-size: 12px;
            color: #666666;
            background-color: rgba(0, 0, 0, 0.05);
            padding: 4px 8px;
            border-radius: 4px;
        }

        .warning-detail-body {
            line-height: 1.6;
            flex: 1; /* 占据剩余空间 */
            overflow-y: auto; /* 添加垂直滚动条 */
            padding-right: 4px; /* 为滚动条留出空间 */
        }

        .warning-detail-text {
            font-size: 14px;
            color: #333333;
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .warning-detail-areas,
        .warning-detail-towns {
            margin-bottom: 12px;
        }

        .warning-detail-label {
            font-size: 13px;
            font-weight: 600;
            color: #1E40AF;
            display: block;
            margin-bottom: 6px;
        }

        .warning-areas-list,
        .warning-towns-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .area-tag,
        .town-tag {
            display: inline-block;
            padding: 4px 8px;
            background-color: #DBEAFE;
            color: #1E40AF;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .area-tag:hover,
        .town-tag:hover {
            background-color: #3B82F6;
            color: #FFFFFF;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        /* 预警高亮样式 */
        .warning-highlight-border {
            animation: warningPulse 2s infinite;
        }

        /* 预警边框脉冲动画 */
        @keyframes warningPulse {
            0%, 100% {
                border-opacity: 0.6;
            }
            50% {
                border-opacity: 1.0;
            }
        }

        /* 预警等级边框颜色 */
        .warning-level-1 {
            border-color: #ef4444 !important; /* 红色 - 一级预警 */
            border-width: 1px !important;
        }

        .warning-level-2 {
            border-color: #f97316 !important; /* 橙色 - 二级预警 */
            border-width: 1px !important;
        }

        .warning-level-3 {
            border-color: #eab308 !important; /* 黄色 - 三级预警 */
            border-width: 1px !important;
        }

        .warning-level-4 {
            border-color: #3b82f6 !important; /* 蓝色 - 四级预警 */
            border-width: 1px !important;
        }

        /* 图例面板 */
        .legend-panel {
            position: absolute;
            top: 0; /* 从按钮顶部开始 */
            right: calc(100% + 8px); /* 在按钮左侧，保持8px间距 */
            width: 0;
            height: auto;
            max-height: 0; /* 收起时高度为0 */
            overflow: hidden;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.6);
            transition: width 0.3s ease, max-height 0.3s ease, opacity 0.3s ease;
            z-index: 1001;
            opacity: 0;
        }

        .legend-panel.expanded {
            width: 300px;
            max-height: 500px; /* 展开时允许向下扩展 */
            opacity: 1;
        }

        .legend-panel-content {
            width: 300px;
            height: auto; /* 自动高度 */
            display: block; /* 确保显示 */
        }

        .legend-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background-color: rgba(30, 64, 175, 0.05);
        }

        .legend-panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #1E40AF;
            margin: 0;
        }

        .legend-close-btn {
            background: none;
            border: none;
            padding: 4px;
            cursor: pointer;
            color: #6B7280;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .legend-close-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: #374151;
        }

        .legend-panel-body {
            padding: 16px;
            max-height: none; /* 移除高度限制 */
            overflow-y: visible; /* 确保内容可见 */
            white-space: normal;
        }

        .legend-section {
            margin-bottom: 20px;
        }

        .legend-section:last-child {
            margin-bottom: 0;
        }

        .legend-section-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin: 0 0 8px 0;
            padding-bottom: 4px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
            color: #4B5563;
        }

        .legend-item:last-child {
            margin-bottom: 0;
        }

        /* 图例元素样式 */
        .legend-point {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .legend-color {
            width: 16px;
            height: 12px;
            border-radius: 2px;
            margin-right: 8px;
            flex-shrink: 0;
            border: 1px solid rgba(0, 0, 0, 0.2);
        }

        .legend-circle {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
            border: 2px solid;
        }

        .legend-warning-border {
            width: 16px;
            height: 12px;
            border: 2px solid;
            border-radius: 2px;
            margin-right: 8px;
            flex-shrink: 0;
            animation: warningPulse 2s infinite;
        }

        /* 具体图例颜色 */
        .legend-disaster {
            background-color: #ef4444;
        }

        .legend-influence {
            border-color: #ef4444;
            background-color: rgba(239, 68, 68, 0.2);
        }

        .legend-defense-1 {
            background-color: #ef4444;
        }

        .legend-defense-2 {
            background-color: #f59e0b;
        }

        .legend-defense-3 {
            background-color: #CCC1DA;
        }

        .legend-warning-1 {
            border-color: #ff0000;
            background-color: rgba(255, 0, 0, 0.2);
        }

        .legend-warning-2 {
            border-color: #ff6600;
            background-color: rgba(255, 102, 0, 0.2);
        }

        .legend-warning-3 {
            border-color: #ffcc00;
            background-color: rgba(255, 204, 0, 0.2);
        }

        .legend-warning-4 {
            border-color: #0066ff;
            background-color: rgba(0, 102, 255, 0.2);
        }

        /* 删除了等级颜色样式，信息条使用统一样式 */

        .warning-banner-text {
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #E5E7EB;
            flex-wrap: nowrap; /* 防止换行 */
            overflow-x: auto; /* 如果内容过多，允许横向滚动 */
        }

        .pagination-btn {
            padding: 4px 8px;
            border: 1px solid #E5E7EB;
            background-color: #FFFFFF;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s ease;
            white-space: nowrap; /* 防止文字换行 */
            flex-shrink: 0; /* 防止按钮被压缩 */
        }

        .pagination-btn:hover {
            border-color: #3B82F6;
            color: #3B82F6;
        }

        .pagination-btn.active {
            background-color: #3B82F6;
            border-color: #3B82F6;
            color: #FFFFFF;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            font-size: 10px;
            color: #6B7280;
            white-space: nowrap;
            flex-shrink: 0;
        }

        /* 预警详情对话框样式 */
        .warning-modal {
            display: none;
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            max-width: 90vw;
            z-index: 10000;
            background-color: #FFFFFF;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
            border: 1px solid #E5E7EB;
        }

        .warning-modal.show {
            display: block;
        }

        .warning-modal-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .warning-modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid #E5E7EB;
            cursor: move;
            background-color: #F9FAFB;
            border-radius: 12px 12px 0 0;
        }

        .warning-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .warning-modal-close {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            font-size: 24px;
            color: #6B7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .warning-modal-close:hover {
            background-color: #F3F4F6;
            color: #374151;
        }

        .warning-modal-body {
            padding: 24px;
        }

        .warning-content {
            line-height: 1.6;
            color: #374151;
            margin-bottom: 24px;
        }

        .warning-towns {
            margin-top: 16px;
        }

        .warning-towns-title {
            font-size: 14px;
            font-weight: 500;
            color: #111827;
            margin-bottom: 8px;
        }

        .town-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .town-tag {
            background-color: #DBEAFE;
            color: #1E40AF;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .town-tag:hover {
            background-color: #3B82F6;
            color: #FFFFFF;
        }

        /* 收起按钮样式 */
        .sidebar-toggle {
            position: absolute;
            top: 30px;
            right: 35px;
            width: 28px;
            height: 28px;
            background-color: #3B82F6;
            border: none;
            border-radius: 50%;
            color: #FFFFFF;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background-color: #2563EB;
            transform: scale(1.1);
        }

        .query-sidebar.collapsed {
            width: 50px;
            padding: 20px 5px;
        }

        .query-sidebar.collapsed .sidebar-section {
            display: none;
        }

        .query-sidebar.collapsed .sidebar-toggle {
            right: 11px;
        }

        /* 当侧边栏收起时，确保地图容器能够正确扩展 */
        .map-container {
            transition: all 0.3s ease;
        }

        .map-stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 12px;
            z-index: 1000;
        }

        .stat-item {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 12px 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            text-align: center;
            min-width: 80px;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #6b7280;
        }

        .navbar {
            background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%);
            border-bottom: 1px solid #1D4ED8;
            padding: 16px 24px;
            box-shadow: 0 2px 8px rgba(30, 64, 175, 0.15);
            height: 64px;
            box-sizing: border-box;
        }

        .navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
        }

        .logo {
            height: 32px;
            margin-right: 16px;
            border-radius: 4px;
        }

        .navbar-title {
            font-size: 20px;
            font-weight: 600;
            color: #FFFFFF;
            margin: 0;
        }

        .navbar-nav {
            display: flex;
            gap: 24px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* 隐藏天地图版权控件 */
        .tdt-control-copyright {
            display: none !important;
        }



        .footer {
            background-color: #f9fafb;
            border-top: 1px solid #e5e7eb;
            padding: 20px 24px;
            text-align: center;
            font-size: 13px;
            color: #6b7280;
            height: 60px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .footer a {
            color: #1e40af;
            text-decoration: none;
            margin: 0 12px;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .query-container {
                flex-direction: column;
                height: auto;
            }

            .query-sidebar {
                width: 100%;
                height: auto;
                max-height: 400px;
            }

            .warning-section {
                max-height: 250px;
            }

            .warning-list {
                max-height: 150px;
            }

            .warning-modal-content {
                width: 95%;
                margin: 20px;
            }

            .town-tags {
                gap: 4px;
            }

            .town-tag {
                font-size: 11px;
                padding: 3px 6px;
            }

            .map-container {
                height: 400px;
            }

            .map-legend {
                top: 10px;
                right: 10px;
                padding: 12px;
            }

            .map-stats {
                bottom: 10px;
                left: 10px;
                flex-wrap: wrap;
            }

            .stat-item {
                min-width: 70px;
                padding: 8px 12px;
            }

            .stat-number {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-content">
            <div class="navbar-brand">
                <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=64&h=32&fit=crop&crop=center" alt="茂名市自然资源局" class="logo">
                <h1 class="navbar-title">茂名市地质灾害预警平台</h1>
            </div>

        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="query-container">
        <!-- 查询工具侧边栏 -->
        <div class="query-sidebar" id="querySidebar">
            <!-- 收起按钮 -->
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-chevron-left"></i>
            </button>

            <!-- 预警信息列表 -->
            <div class="sidebar-section warning-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    预警信息
                </h3>

                <!-- 区域筛选 -->
                <div class="form-group">
                    <label class="form-label">选择区县</label>
                    <select class="form-select" id="warningCountySelect">
                        <option value="">全部区县</option>
                        <option value="maonan">茂南区</option>
                        <option value="dianbai">电白区</option>
                        <option value="gaozhou">高州市</option>
                        <option value="huazhou">化州市</option>
                        <option value="xinyi">信宜市</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">选择镇街</label>
                    <select class="form-select" id="warningTownSelect">
                        <option value="">全部镇街</option>
                    </select>
                </div>

                <!-- 预警信息列表 -->
                <div class="warning-list" id="warningList">
                    <!-- 预警信息项将通过JavaScript动态生成 -->
                </div>

                <!-- 分页控件 -->
                <div class="pagination" id="warningPagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 图例说明 -->
            <div class="sidebar-section legend-section">
                <h3 class="sidebar-title">
                    <i class="fas fa-info-circle"></i>
                    图例说明
                </h3>
                <div class="sidebar-legend">
                    <div class="legend-item">
                        <div class="legend-point legend-disaster"></div>
                        <span>地质灾害点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color legend-high"></div>
                        <span>高风险区域</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color legend-medium"></div>
                        <span>中风险区域</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color legend-low"></div>
                        <span>低风险区域</span>
                    </div>
                    <!-- 预警状态图例 -->
                    <div class="legend-divider" id="warningLegendDivider" style="display: none;">
                        <hr style="margin: 8px 0; border: none; border-top: 1px solid #e5e7eb;">
                        <small style="color: #6b7280; font-size: 11px;">预警状态</small>
                    </div>
                    <div class="legend-item" id="warningLegendItem" style="display: none;">
                        <div class="legend-warning-border" id="warningLegendBorder"></div>
                        <span id="warningLegendText">预警范围内风险区</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 地图展示区域 -->
        <div class="map-container">
            <!-- 天地图容器 -->
            <div id="mapDiv"></div>



            <!-- 浮动预警信息区域 - 独立警铃+信息条 -->
            <div class="map-warning-banner" id="mapWarningBanner">
                <!-- 信息条主体容器 -->
                <div class="warning-banner-main">
                    <!-- 独立警铃图标 -->
                    <i class="fas fa-bell warning-banner-icon" id="warningBannerIcon"></i>

                    <!-- 信息条 -->
                    <div class="warning-banner-content">
                        <!-- 固定部分：预警等级 -->
                        <div class="warning-banner-fixed">
                            <span class="warning-level-label" id="warningLevelLabel">四级</span>
                            <span id="warningFixedText">预警 -</span>
                        </div>

                        <!-- 滚动部分：镇街信息 -->
                        <div class="warning-banner-scrolling-wrapper">
                            <div class="warning-banner-scrolling" id="warningBannerScrolling">
                                <div class="warning-banner-text" id="warningBannerText">
                                    涉及镇街信息
                                </div>
                            </div>
                        </div>

                        <!-- 展开/收起按钮 -->
                        <button class="warning-expand-btn" id="warningExpandBtn" title="展开详情">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                </div>

                <!-- 展开的详细内容区域 -->
                <div class="warning-detail-panel" id="warningDetailPanel">
                    <div class="warning-detail-content">
                        <div class="warning-detail-header">
                            <h4 class="warning-detail-title" id="warningDetailTitle">预警信息详情</h4>
                            <span class="warning-detail-time" id="warningDetailTime">发布时间</span>
                        </div>
                        <div class="warning-detail-body">
                            <p class="warning-detail-text" id="warningDetailText">预警详细内容</p>
                            <div class="warning-detail-areas">
                                <span class="warning-detail-label">涉及区域：</span>
                                <div class="warning-areas-list" id="warningAreasList">
                                    <!-- 区域标签将通过JavaScript动态生成 -->
                                </div>
                            </div>
                            <div class="warning-detail-towns">
                                <span class="warning-detail-label">涉及镇街：</span>
                                <div class="warning-towns-list" id="warningTownsList">
                                    <!-- 镇街标签将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- 地图工具按钮 -->
            <div class="map-tools">
                <button class="tool-button active" id="disasterToolBtn" title="地质灾害点">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>灾害点</span>
                </button>
                <button class="tool-button active" id="riskToolBtn" title="风险防范区">
                    <i class="fas fa-shield-alt"></i>
                    <span>防范区</span>
                </button>
                <button class="tool-button" id="legendBtn" title="图例说明">
                    <i class="fas fa-list-ul"></i>
                    <span>图例</span>
                    <!-- 图例面板 -->
                    <div class="legend-panel" id="legendPanel">
                        <div class="legend-panel-content">
                            <div class="legend-panel-header">
                                <h4 class="legend-panel-title">图例说明</h4>
                                <button class="legend-close-btn" id="legendCloseBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="legend-panel-body">
                                <!-- 地质灾害点 -->
                                <div class="legend-section">
                                    <h5 class="legend-section-title">地质灾害点</h5>
                                    <div class="legend-item">
                                        <div class="legend-point legend-disaster"></div>
                                        <span>地质灾害点</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-circle legend-influence"></div>
                                        <span>地质灾害点影响范围</span>
                                    </div>
                                </div>

                                <!-- 风险防范区 -->
                                <div class="legend-section">
                                    <h5 class="legend-section-title">风险防范区</h5>
                                    <div class="legend-item">
                                        <div class="legend-color legend-defense-1"></div>
                                        <span>一级防范区（高风险）</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color legend-defense-2"></div>
                                        <span>二级防范区（中风险）</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color legend-defense-3"></div>
                                        <span>三级防范区（低风险）</span>
                                    </div>
                                </div>

                                <!-- 预警状态 -->
                                <div class="legend-section" id="warningLegendSection" style="display: none;">
                                    <h5 class="legend-section-title">预警状态</h5>
                                    <div class="legend-item">
                                        <div class="legend-warning-border legend-warning-1"></div>
                                        <span id="warningLegend1Text">一级预警范围内风险区</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-warning-border legend-warning-2"></div>
                                        <span id="warningLegend2Text">二级预警范围内风险区</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-warning-border legend-warning-3"></div>
                                        <span id="warningLegend3Text">三级预警范围内风险区</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-warning-border legend-warning-4"></div>
                                        <span id="warningLegend4Text">四级预警范围内风险区</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </button>
            </div>

            <!-- 地图统计信息 -->
            <div class="map-stats">
                <div class="stat-item">
                    <div class="stat-number">74006</div>
                    <div class="stat-label">灾害点</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">209</div>
                    <div class="stat-label">防范区</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预警详情对话框 -->
    <div class="warning-modal" id="warningModal">
        <div class="warning-modal-header">
            <div class="warning-modal-title" id="modalTitle">
                <div class="warning-level" id="modalLevel"></div>
                预警详情
            </div>
            <button class="warning-modal-close" id="modalClose">&times;</button>
        </div>
        <div class="warning-modal-content">
            <div class="warning-modal-body">
                <div class="warning-content" id="modalContent">
                    <!-- 预警内容将通过JavaScript填充 -->
                </div>
                <div class="warning-towns">
                    <div class="warning-towns-title">涉及镇街（点击可定位）：</div>
                    <div class="town-tags" id="modalTowns">
                        <!-- 镇街标签将通过JavaScript填充 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部信息 -->
    <footer class="footer">
        <p>© 2025 茂名市自然资源局 | 服务热线：0668-12345678 |
        <a href="#">使用说明</a> |
        <a href="#">联系我们</a> |
        <a href="#">隐私政策</a></p>
    </footer>

    <script>
        // 预警数据已从外部JS文件引入

        // 镇街坐标数据已从外部JS文件引入

        // 预警信息管理
        var currentPage = 1;
        var pageSize = 10;
        var filteredWarnings = warningData;

        // 天地图初始化
        var map;
        var disasterLayer;
        var riskLayer;
        var boundaryLayer;
        var countyBoundaryLayer;

        function initMap() {
            // 创建影像底图URL
            var imageURL = "http://t0.tianditu.gov.cn/img_w/wmts?" +
                "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
                "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7b0f309efd0864b4ce49fafda0302277";

            // 创建影像标注URL
            var ciaURL = "http://t0.tianditu.gov.cn/cia_w/wmts?" +
                "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
                "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7b0f309efd0864b4ce49fafda0302277";

            // 创建自定义图层对象
            var imgLayer = new T.TileLayer(imageURL, {minZoom: 1, maxZoom: 18});
            var ciaLayer = new T.TileLayer(ciaURL, {minZoom: 1, maxZoom: 18});

            // 初始化地图对象，配置图层
            var config = {layers: [imgLayer, ciaLayer]};
            map = new T.Map("mapDiv", config);

            // 设置显示地图的中心点和级别（茂名市）
            map.centerAndZoom(new T.LngLat(110.9255, 21.6687), 11);

            // 允许鼠标滚轮缩放地图
            map.enableScrollWheelZoom();

            // 添加地图控件
            map.addControl(new T.Control.Zoom());
            // 移除比例尺控件，避免与统计信息重叠

            // 监听地图缩放事件，输出当前缩放级别并控制行政边界显示
            map.addEventListener("zoomend", function() {
                var currentZoom = map.getZoom();
                console.log("当前地图缩放级别：" + currentZoom);

                // 控制茂名市行政边界显示（7-9级）
                if (currentZoom >= 7 && currentZoom <= 9) {
                    if (boundaryLayer && !map.hasLayer(boundaryLayer)) {
                        map.addLayer(boundaryLayer);
                        console.log("显示茂名市行政边界");
                    }
                    // 隐藏县级边界
                    if (countyBoundaryLayer && map.hasLayer(countyBoundaryLayer)) {
                        map.removeLayer(countyBoundaryLayer);
                        console.log("隐藏县级边界");
                    }
                } else if (currentZoom > 9) {
                    // 大于9级显示县级边界
                    if (countyBoundaryLayer && !map.hasLayer(countyBoundaryLayer)) {
                        map.addLayer(countyBoundaryLayer);
                        console.log("显示县级边界");
                    }
                    // 隐藏市级边界
                    if (boundaryLayer && map.hasLayer(boundaryLayer)) {
                        map.removeLayer(boundaryLayer);
                        console.log("隐藏茂名市行政边界");
                    }
                } else {
                    // 小于7级隐藏所有边界
                    if (boundaryLayer && map.hasLayer(boundaryLayer)) {
                        map.removeLayer(boundaryLayer);
                        console.log("隐藏茂名市行政边界");
                    }
                    if (countyBoundaryLayer && map.hasLayer(countyBoundaryLayer)) {
                        map.removeLayer(countyBoundaryLayer);
                        console.log("隐藏县级边界");
                    }
                }
            });

            // 输出初始缩放级别并检查边界显示
            var initialZoom = map.getZoom();
            console.log("初始地图缩放级别：" + initialZoom);

            // 检查初始状态是否需要显示边界
            if (initialZoom >= 7 && initialZoom <= 9) {
                if (boundaryLayer && !map.hasLayer(boundaryLayer)) {
                    map.addLayer(boundaryLayer);
                    console.log("初始显示茂名市行政边界");
                }
            } else if (initialZoom > 9) {
                if (countyBoundaryLayer && !map.hasLayer(countyBoundaryLayer)) {
                    map.addLayer(countyBoundaryLayer);
                    console.log("初始显示县级边界");
                }
            }

            // 初始化图层
            initLayers();

            // 添加全局鼠标事件来处理tooltip
            document.addEventListener('mousemove', function(e) {
                var tooltip = document.getElementById('hoverTooltip');
                if (tooltip) {
                    tooltip.style.left = (e.clientX + 10) + 'px';
                    tooltip.style.top = (e.clientY - tooltip.offsetHeight - 10) + 'px';

                    // 确保不超出屏幕边界
                    var rect = tooltip.getBoundingClientRect();
                    if (rect.right > window.innerWidth) {
                        tooltip.style.left = (e.clientX - tooltip.offsetWidth - 10) + 'px';
                    }
                    if (rect.top < 0) {
                        tooltip.style.top = (e.clientY + 10) + 'px';
                    }
                }
            });
        }



        function initLayers() {
            // 创建地质灾害点图层
            disasterLayer = new T.LayerGroup();
            map.addLayer(disasterLayer);

            // 创建风险防范区图层
            riskLayer = new T.LayerGroup();
            map.addLayer(riskLayer);

            // 添加示例数据
            addSampleData();
        }

        function addSampleData() {
            // 初始化预警区域数组
            window.warningPolygons = [];

            // 使用外部JS文件中的地质灾害点数据

            disasterPoints.forEach(function(point) {
                // 创建中心点标记
                var marker = new T.Marker(new T.LngLat(point.lng, point.lat));

                // 创建100米影响范围圆圈（统一颜色）
                var circle = new T.Circle(new T.LngLat(point.lng, point.lat), 100, {
                    color: "#3498db",
                    weight: 2,
                    opacity: 0.8,
                    fillColor: "#3498db",
                    fillOpacity: 0.2
                });

                // 创建信息窗口
                var infoWin = new T.InfoWindow();
                infoWin.setContent(`
                    <div style="padding: 12px; min-width: 180px; font-family: Arial, sans-serif;">
                        <h4 style="color: #1e40af; margin: 0 0 8px 0; font-size: 14px;">${point.name}</h4>
                        <p style="margin: 4px 0; font-size: 13px;"><strong>灾害类型:</strong> ${point.type}</p>
                        <p style="margin: 4px 0; font-size: 13px;"><strong>风险等级:</strong> <span style="color: ${getRiskColor(point.risk)}">${point.risk}</span></p>
                        <p style="margin: 4px 0; font-size: 13px;"><strong>影响范围:</strong> 半径100米</p>
                        <p style="margin: 4px 0; font-size: 12px; color: #6b7280;">经度: ${point.lng} | 纬度: ${point.lat}</p>
                    </div>
                `);

                // 添加点击事件 - 平移并缩放地图到17级，然后显示信息框
                marker.addEventListener("click", function() {
                    // 平移并缩放地图到灾害点位置（17级）
                    map.centerAndZoom(new T.LngLat(point.lng, point.lat), 17);
                    // 延迟显示信息框，等待平移和缩放动画完成
                    setTimeout(function() {
                        marker.openInfoWindow(infoWin);
                    }, 500);
                });

                circle.addEventListener("click", function() {
                    // 平移并缩放地图到灾害点位置（17级）
                    map.centerAndZoom(new T.LngLat(point.lng, point.lat), 17);
                    // 延迟显示信息框，等待平移和缩放动画完成
                    setTimeout(function() {
                        marker.openInfoWindow(infoWin);
                    }, 500);
                });

                // 添加到图层
                disasterLayer.addLayer(marker);
                disasterLayer.addLayer(circle);
            });

            // 使用外部JS文件中的风险防范区数据，需要转换坐标格式

            riskAreas.forEach(function(area) {
                // 转换坐标格式：从{lng, lat}对象转换为T.LngLat对象
                var tiandituPoints = area.points.map(function(point) {
                    return new T.LngLat(point.lng, point.lat);
                });

                // 检查是否在预警范围内
                var isInWarningArea = isAreaInWarning(area);
                var polygonOptions = {
                    color: getRiskColor(area.risk),
                    weight: 2,
                    opacity: 0.8,
                    fillColor: getRiskColor(area.risk),
                    fillOpacity: 0.3
                };

                // 如果在预警范围内，应用预警高亮样式
                if (isInWarningArea.inWarning) {
                    polygonOptions = getWarningHighlightStyle(area, isInWarningArea.warningLevel);
                }

                var polygon = new T.Polygon(tiandituPoints, polygonOptions);

                // 如果是预警区域，保存引用用于动画
                if (isInWarningArea.inWarning) {
                    window.warningPolygons.push(polygon);
                }

                // 创建信息窗口
                var infoWin = new T.InfoWindow();
                var warningInfo = isInWarningArea.inWarning ?
                    '<p style="color: #ef4444; font-weight: bold;">⚠️ 当前在预警范围内</p>' : '';

                // 获取防御措施
                var defenseMeasure = getDefenseMeasure(
                    isInWarningArea.inWarning ? isInWarningArea.warningLevel : null,
                    area.defenseLevel
                );

                infoWin.setContent(`
                    <div style="padding: 10px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        <h4 style="margin: 0 0 8px 0; color: #1f2937;">${area.name}</h4>
                        <p style="margin: 4px 0;">风险等级: <span style="color: ${getRiskColor(area.risk)}; font-weight: bold;">${area.risk}</span></p>
                        <p style="margin: 4px 0;">防范区等级: <span style="color: #1E40AF; font-weight: bold;">${area.defenseLevel}</span></p>
                        <p style="margin: 4px 0;">覆盖面积: ${area.area}</p>
                        <p style="margin: 4px 0;">影响人口: ${area.population}</p>
                        ${warningInfo}
                        <div style="
                            margin-top: 12px;
                            padding: 12px;
                            background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
                            border-radius: 8px;
                            border: 2px solid #f59e0b;
                            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
                        ">
                            <div style="
                                display: flex;
                                align-items: center;
                                margin-bottom: 6px;
                            ">
                                <span style="
                                    font-size: 16px;
                                    margin-right: 6px;
                                ">🛡️</span>
                                <p style="
                                    margin: 0;
                                    font-weight: bold;
                                    color: #92400e;
                                    font-size: 14px;
                                ">防御措施</p>
                            </div>
                            <p style="
                                margin: 0;
                                color: #92400e;
                                line-height: 1.5;
                                font-weight: 600;
                                font-size: 13px;
                            ">${defenseMeasure}</p>
                        </div>
                    </div>
                `);

                // 创建悬停提示信息
                var hoverTooltip = createHoverTooltip(area, isInWarningArea);

                // 简化的事件绑定 - 保存区域信息用于全局检测
                polygon._areaInfo = {
                    name: area.name,
                    risk: area.risk,
                    population: area.population,
                    isInWarning: isInWarningArea.inWarning,
                    tooltip: hoverTooltip
                };

                // 使用天地图的标准事件
                polygon.addEventListener("mouseover", function(e) {
                    console.log('鼠标悬停事件触发:', area.name);
                    window.currentHoverArea = polygon._areaInfo;
                    showHoverTooltipSimple(hoverTooltip);
                    highlightArea(polygon, true);
                });

                polygon.addEventListener("mouseout", function() {
                    console.log('鼠标离开事件触发:', area.name);
                    window.currentHoverArea = null;
                    hideHoverTooltip();
                    highlightArea(polygon, false);
                });

                // 点击事件
                polygon.addEventListener("click", function() {
                    // 隐藏悬停提示
                    hideHoverTooltip();

                    // 计算风险区的中心点
                    var centerLng = area.points.reduce(function(sum, point) { return sum + point.lng; }, 0) / area.points.length;
                    var centerLat = area.points.reduce(function(sum, point) { return sum + point.lat; }, 0) / area.points.length;
                    var centerPoint = new T.LngLat(centerLng, centerLat);

                    // 平移并缩放地图到风险区中心（12级）
                    map.centerAndZoom(centerPoint, 12);
                    // 延迟显示信息框，等待平移和缩放动画完成
                    setTimeout(function() {
                        polygon.openInfoWindow(infoWin);
                    }, 500);
                });

                riskLayer.addLayer(polygon);
            });

            // 如果有预警区域，启动脉冲动画
            if (window.warningPolygons && window.warningPolygons.length > 0) {
                startWarningPulseAnimation();
            }
        }

        // 创建悬停提示内容
        function createHoverTooltip(area, warningInfo) {
            var warningStatus = warningInfo.inWarning ?
                '<div style="color: #ef4444; font-weight: bold; margin-top: 4px;">⚠️ 预警范围内</div>' : '';

            // 获取简化的防御措施（只显示前20个字符）
            var defenseMeasure = getDefenseMeasure(
                warningInfo.inWarning ? warningInfo.warningLevel : null,
                area.defenseLevel
            );
            var shortMeasure = defenseMeasure.length > 20 ?
                defenseMeasure.substring(0, 20) + '...' : defenseMeasure;

            return `
                <div style="
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 12px;
                    line-height: 1.4;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                    max-width: 220px;
                    pointer-events: none;
                ">
                    <div style="font-weight: bold; margin-bottom: 4px;">${area.name}</div>
                    <div>风险等级: <span style="color: ${getRiskColor(area.risk)}">${area.risk}</span></div>
                    <div>防范区等级: <span style="color: #60a5fa">${area.defenseLevel}</span></div>
                    <div>影响人口: ${area.population}</div>
                    <div style="margin-top: 4px; color: #fbbf24;">防御措施: ${shortMeasure}</div>
                    ${warningStatus}
                    <div style="margin-top: 6px; font-size: 11px; color: #ccc;">点击查看详细信息</div>
                </div>
            `;
        }

        // 显示悬停提示
        function showHoverTooltip(event, content) {
            console.log('显示悬停提示'); // 调试日志
            hideHoverTooltip(); // 先隐藏之前的提示

            var tooltip = document.createElement('div');
            tooltip.id = 'hoverTooltip';
            tooltip.innerHTML = content;
            tooltip.style.position = 'fixed';
            tooltip.style.zIndex = '10000';
            tooltip.style.pointerEvents = 'none';

            document.body.appendChild(tooltip);

            // 获取鼠标位置 - 处理不同的事件对象
            var mouseX, mouseY;
            if (event.clientX !== undefined) {
                mouseX = event.clientX;
                mouseY = event.clientY;
            } else if (event.pageX !== undefined) {
                mouseX = event.pageX;
                mouseY = event.pageY;
            } else {
                // 如果无法获取鼠标位置，使用屏幕中心
                mouseX = window.innerWidth / 2;
                mouseY = window.innerHeight / 2;
            }

            console.log('鼠标位置:', mouseX, mouseY); // 调试日志

            tooltip.style.left = (mouseX + 10) + 'px';
            tooltip.style.top = (mouseY - tooltip.offsetHeight - 10) + 'px';

            // 确保不超出屏幕边界
            setTimeout(function() {
                var rect = tooltip.getBoundingClientRect();
                if (rect.right > window.innerWidth) {
                    tooltip.style.left = (mouseX - tooltip.offsetWidth - 10) + 'px';
                }
                if (rect.top < 0) {
                    tooltip.style.top = (mouseY + 10) + 'px';
                }
            }, 10);
        }

        // 简化的tooltip显示函数
        function showHoverTooltipSimple(content) {
            console.log('显示简化tooltip');
            hideHoverTooltip(); // 先隐藏之前的提示

            var tooltip = document.createElement('div');
            tooltip.id = 'hoverTooltip';
            tooltip.innerHTML = content;
            tooltip.style.position = 'fixed';
            tooltip.style.zIndex = '10000';
            tooltip.style.pointerEvents = 'none';
            tooltip.style.left = '50px';
            tooltip.style.top = '50px';

            document.body.appendChild(tooltip);
        }

        // 隐藏悬停提示
        function hideHoverTooltip() {
            var tooltip = document.getElementById('hoverTooltip');
            if (tooltip) {
                tooltip.remove();
            }
        }

        // 高亮区域
        function highlightArea(polygon, highlight) {
            if (!polygon || !polygon.setStyle) return;

            if (highlight) {
                // 高亮时增加边框宽度和透明度
                var currentStyle = polygon.getStyle ? polygon.getStyle() : {};
                polygon.setStyle({
                    ...currentStyle,
                    weight: (currentStyle.weight || 2) + 2,
                    opacity: 1.0,
                    fillOpacity: Math.min((currentStyle.fillOpacity || 0.3) + 0.2, 0.8)
                });
            } else {
                // 恢复原始样式（如果不是预警区域）
                if (!window.warningPolygons || !window.warningPolygons.includes(polygon)) {
                    polygon.setStyle({
                        weight: 2,
                        opacity: 0.8,
                        fillOpacity: 0.3
                    });
                }
            }
        }

        // getRiskColor和getRiskMeasure函数已从外部JS文件引入

        // 判断风险防范区是否在预警范围内
        function isAreaInWarning(area) {
            // 查找生效的预警信息
            var activeWarning = warningData.find(function(warning) {
                return warning.status === '生效';
            });

            if (!activeWarning || !area.towns) {
                return { inWarning: false, warningLevel: null };
            }

            // 检查风险防范区的镇街是否在预警涉及的镇街中
            var hasWarningTown = area.towns.some(function(town) {
                return activeWarning.towns.includes(town);
            });

            return {
                inWarning: hasWarningTown,
                warningLevel: activeWarning.level
            };
        }

        // 获取预警高亮样式
        function getWarningHighlightStyle(area, warningLevel) {
            var baseColor = getRiskColor(area.risk); // 保持原有风险等级颜色
            var warningBorderColor;

            // 根据预警等级设置边框颜色
            switch(warningLevel) {
                case 1:
                    warningBorderColor = '#ff0000'; // 鲜红色边框
                    break;
                case 2:
                    warningBorderColor = '#ff6600'; // 鲜橙色边框
                    break;
                case 3:
                    warningBorderColor = '#ffcc00'; // 鲜黄色边框
                    break;
                case 4:
                    warningBorderColor = '#0066ff'; // 鲜蓝色边框
                    break;
                default:
                    warningBorderColor = '#ff0000';
            }

            return {
                color: warningBorderColor,
                weight: 4, // 适中的边框宽度
                opacity: 1.0,
                fillColor: baseColor, // 保持原有风险等级颜色
                fillOpacity: 0.5 // 稍微提升透明度以突出预警状态
            };
        }

        // 启动预警区域脉冲动画
        function startWarningPulseAnimation() {
            if (window.warningPulseInterval) {
                clearInterval(window.warningPulseInterval);
            }

            var pulseState = 0; // 0-1之间的值
            var pulseDirection = 1; // 1为增加，-1为减少

            window.warningPulseInterval = setInterval(function() {
                pulseState += pulseDirection * 0.08; // 更快的变化速度

                if (pulseState >= 1) {
                    pulseState = 1;
                    pulseDirection = -1;
                } else if (pulseState <= 0.3) { // 更大的透明度变化范围
                    pulseState = 0.3;
                    pulseDirection = 1;
                }

                // 更新所有预警区域的透明度
                updateWarningAreasOpacity(pulseState);
            }, 80); // 稍慢的更新频率，更平滑
        }

        // 停止预警区域脉冲动画
        function stopWarningPulseAnimation() {
            if (window.warningPulseInterval) {
                clearInterval(window.warningPulseInterval);
                window.warningPulseInterval = null;
            }
        }

        // 更新预警区域的透明度和边框宽度
        function updateWarningAreasOpacity(opacity) {
            if (!window.warningPolygons) return;

            window.warningPolygons.forEach(function(polygon) {
                if (polygon && polygon.setStyle) {
                    // 计算动态边框宽度（3-5px之间变化，更温和）
                    var dynamicWeight = 3 + (opacity * 2);

                    polygon.setStyle({
                        opacity: opacity,
                        fillOpacity: 0.3 + (opacity * 0.3), // 填充透明度在0.3-0.6之间变化
                        weight: dynamicWeight // 边框宽度温和变化
                    });
                }
            });
        }

        // 显示当前生效的预警信息条
        function showActiveWarningBanner() {
            // 查找生效的预警信息
            var activeWarning = warningData.find(function(warning) {
                return warning.status === '生效';
            });

            var banner = document.getElementById('mapWarningBanner');
            var iconElement = document.getElementById('warningBannerIcon');
            var levelLabelElement = document.getElementById('warningLevelLabel');
            var fixedTextElement = document.getElementById('warningFixedText');
            var textElement = document.getElementById('warningBannerText');
            var scrollingElement = document.getElementById('warningBannerScrolling');

            if (activeWarning) {
                // 有生效的预警信息
                var townsText = activeWarning.towns.join('、');

                // 设置固定部分
                levelLabelElement.textContent = activeWarning.levelDesc;
                levelLabelElement.className = 'warning-level-label level-' + activeWarning.level;
                fixedTextElement.textContent = '预警 -';

                // 设置滚动部分（镇街信息）
                textElement.textContent = townsText;

                // 设置警铃图标颜色
                iconElement.className = 'fas fa-bell warning-banner-icon level-' + activeWarning.level;

                // 填充详细内容面板
                populateWarningDetailPanel(activeWarning);

            } else {
                // 没有生效的预警信息
                levelLabelElement.textContent = '无';
                levelLabelElement.className = 'warning-level-label';
                levelLabelElement.style.backgroundColor = '#999999';
                levelLabelElement.style.color = '#FFFFFF';
                fixedTextElement.textContent = '预警 -';
                textElement.textContent = '当前没有地质灾害预警';

                // 设置警铃图标为灰色
                iconElement.className = 'fas fa-bell warning-banner-icon no-warning';
            }

            // 显示信息条
            banner.className = 'map-warning-banner show';

            // 检查是否需要滚动（只对滚动部分的镇街信息进行滚动检查）
            setTimeout(function() {
                var containerWidth = scrollingElement.offsetWidth;
                var textWidth = textElement.offsetWidth;

                if (textWidth > containerWidth) {
                    // 内容超出容器宽度，启用滚动
                    scrollingElement.classList.remove('no-scroll');
                } else {
                    // 内容未超出，不需要滚动
                    scrollingElement.classList.add('no-scroll');
                }
            }, 100);

            // 初始化展开按钮事件监听器
            initWarningExpandButton();

            // 更新预警图例显示
            updateWarningLegend(activeWarning);
        }

        // 更新预警图例显示
        function updateWarningLegend(warning) {
            var legendDivider = document.getElementById('warningLegendDivider');
            var legendItem = document.getElementById('warningLegendItem');
            var legendBorder = document.getElementById('warningLegendBorder');
            var legendText = document.getElementById('warningLegendText');

            if (warning && warning.status === '生效') {
                // 显示预警图例
                legendDivider.style.display = 'block';
                legendItem.style.display = 'flex';

                // 设置边框颜色和样式
                var borderColor;
                var levelText;
                switch(warning.level) {
                    case 1:
                        borderColor = '#ef4444';
                        levelText = '一级预警范围内风险区';
                        break;
                    case 2:
                        borderColor = '#f97316';
                        levelText = '二级预警范围内风险区';
                        break;
                    case 3:
                        borderColor = '#eab308';
                        levelText = '三级预警范围内风险区';
                        break;
                    case 4:
                        borderColor = '#3b82f6';
                        levelText = '四级预警范围内风险区';
                        break;
                    default:
                        borderColor = '#6b7280';
                        levelText = '预警范围内风险区';
                }

                legendBorder.style.borderColor = borderColor;
                legendBorder.style.backgroundColor = borderColor + '20'; // 添加透明度
                legendText.textContent = levelText;
            } else {
                // 隐藏预警图例
                legendDivider.style.display = 'none';
                legendItem.style.display = 'none';
            }
        }

        // 填充预警详细内容面板
        function populateWarningDetailPanel(warning) {
            var titleElement = document.getElementById('warningDetailTitle');
            var timeElement = document.getElementById('warningDetailTime');
            var textElement = document.getElementById('warningDetailText');
            var areasListElement = document.getElementById('warningAreasList');
            var townsListElement = document.getElementById('warningTownsList');

            // 设置标题和时间
            titleElement.textContent = warning.title;
            timeElement.textContent = '发布时间：' + warning.publishTime;

            // 设置详细内容
            textElement.textContent = warning.content;

            // 生成区域标签
            areasListElement.innerHTML = '';
            warning.areas.forEach(function(area) {
                var areaTag = document.createElement('span');
                areaTag.className = 'area-tag';
                areaTag.textContent = area;
                areaTag.addEventListener('click', function() {
                    // 点击区域标签时定位到该区域
                    locateToArea(area);
                    // 自动收起详细面板
                    closeWarningDetailPanel();
                });
                areasListElement.appendChild(areaTag);
            });

            // 生成镇街标签
            townsListElement.innerHTML = '';
            warning.towns.forEach(function(town) {
                var townTag = document.createElement('span');
                townTag.className = 'town-tag';
                townTag.textContent = town;
                townTag.addEventListener('click', function() {
                    // 点击镇街标签时定位到该镇街
                    locateToTown(town);
                    // 自动收起详细面板
                    closeWarningDetailPanel();
                });
                townsListElement.appendChild(townTag);
            });
        }

        // 初始化预警展开按钮
        function initWarningExpandButton() {
            var expandBtn = document.getElementById('warningExpandBtn');
            var detailPanel = document.getElementById('warningDetailPanel');
            var bannerContent = document.querySelector('.warning-banner-content');

            if (expandBtn && detailPanel && bannerContent) {
                expandBtn.addEventListener('click', function() {
                    var isExpanded = detailPanel.classList.contains('expanded');

                    if (isExpanded) {
                        // 收起
                        detailPanel.classList.remove('expanded');
                        expandBtn.classList.remove('expanded');
                        expandBtn.title = '展开详情';
                        bannerContent.classList.remove('panel-expanded');
                    } else {
                        // 展开
                        detailPanel.classList.add('expanded');
                        expandBtn.classList.add('expanded');
                        expandBtn.title = '收起详情';
                        bannerContent.classList.add('panel-expanded');
                    }
                });
            }
        }

        // 关闭预警详细面板
        function closeWarningDetailPanel() {
            var expandBtn = document.getElementById('warningExpandBtn');
            var detailPanel = document.getElementById('warningDetailPanel');
            var bannerContent = document.querySelector('.warning-banner-content');

            if (expandBtn && detailPanel && bannerContent) {
                // 收起面板
                detailPanel.classList.remove('expanded');
                expandBtn.classList.remove('expanded');
                expandBtn.title = '展开详情';
                bannerContent.classList.remove('panel-expanded');
            }
        }

        // 定位到指定区域
        function locateToArea(areaName) {
            // 根据区域名称定位地图
            var coordinates = getAreaCoordinates(areaName);
            if (coordinates && map) {
                map.centerAndZoom(new T.LngLat(coordinates.lng, coordinates.lat), coordinates.zoom);
            }
        }

        // 定位到指定镇街
        function locateToTown(townName) {
            // 根据镇街名称定位地图
            var coordinates = getTownCoordinates(townName);
            if (coordinates && map) {
                map.centerAndZoom(new T.LngLat(coordinates.lng, coordinates.lat), coordinates.zoom);
            }
        }

        // 获取区域坐标（简化版本，实际应该从数据库获取）
        function getAreaCoordinates(areaName) {
            var areaCoords = {
                '信宜市': { lng: 110.9444, lat: 22.3536, zoom: 11 },
                '高州市': { lng: 110.8467, lat: 21.9139, zoom: 11 },
                '电白区': { lng: 111.0031, lat: 21.5128, zoom: 11 },
                '化州市': { lng: 110.6339, lat: 21.6631, zoom: 11 }
            };
            return areaCoords[areaName];
        }

        // 获取镇街坐标（简化版本，实际应该从数据库获取）
        function getTownCoordinates(townName) {
            var townCoords = {
                '合水镇': { lng: 110.8500, lat: 22.4000, zoom: 13 },
                '钱排镇': { lng: 110.7500, lat: 22.3500, zoom: 13 },
                '新宝镇': { lng: 110.9000, lat: 22.2500, zoom: 13 },
                '大成镇': { lng: 110.8000, lat: 22.2000, zoom: 13 },
                '大坡镇': { lng: 110.9000, lat: 21.9500, zoom: 13 },
                '马贵镇': { lng: 110.8500, lat: 21.8500, zoom: 13 },
                '古丁镇': { lng: 110.7500, lat: 21.8000, zoom: 13 },
                '深镇镇': { lng: 110.9500, lat: 21.9000, zoom: 13 },
                '罗坑镇': { lng: 111.1000, lat: 21.6000, zoom: 13 },
                '望夫镇': { lng: 111.0500, lat: 21.5500, zoom: 13 },
                '那霍镇': { lng: 111.1500, lat: 21.4500, zoom: 13 }
            };
            return townCoords[townName];
        }

        // 初始化图例按钮
        function initLegendButton() {
            var legendBtn = document.getElementById('legendBtn');
            var legendPanel = document.getElementById('legendPanel');
            var legendCloseBtn = document.getElementById('legendCloseBtn');

            if (legendBtn && legendPanel && legendCloseBtn) {
                // 图例按钮点击事件
                legendBtn.addEventListener('click', function() {
                    toggleLegendPanel();
                });

                // 关闭按钮点击事件
                legendCloseBtn.addEventListener('click', function() {
                    closeLegendPanel();
                });

                // 点击面板外部关闭
                document.addEventListener('click', function(e) {
                    if (!legendPanel.contains(e.target) && !legendBtn.contains(e.target)) {
                        closeLegendPanel();
                    }
                });
            }
        }

        // 打开图例面板
        function openLegendPanel() {
            var legendBtn = document.getElementById('legendBtn');
            var legendPanel = document.getElementById('legendPanel');

            legendPanel.classList.add('expanded');
            legendBtn.classList.add('active');

            // 更新预警图例显示
            updateLegendWarningSection();
        }

        // 关闭图例面板
        function closeLegendPanel() {
            var legendBtn = document.getElementById('legendBtn');
            var legendPanel = document.getElementById('legendPanel');

            legendPanel.classList.remove('expanded');
            legendBtn.classList.remove('active');
        }

        // 切换图例面板
        function toggleLegendPanel() {
            var legendPanel = document.getElementById('legendPanel');
            var isExpanded = legendPanel.classList.contains('expanded');

            if (isExpanded) {
                closeLegendPanel();
            } else {
                openLegendPanel();
            }
        }

        // 更新图例中的预警部分
        function updateLegendWarningSection() {
            var warningSection = document.getElementById('warningLegendSection');

            // 查找生效的预警信息
            var activeWarning = warningData.find(function(warning) {
                return warning.status === '生效';
            });

            if (activeWarning) {
                // 显示预警图例部分
                warningSection.style.display = 'block';

                // 更新当前预警等级的文字
                var currentLevelText = document.getElementById('warningLegend' + activeWarning.level + 'Text');
                if (currentLevelText) {
                    currentLevelText.style.fontWeight = 'bold';
                    currentLevelText.style.color = '#1E40AF';
                }
            } else {
                // 隐藏预警图例部分
                warningSection.style.display = 'none';
            }
        }

        // 图层控制功能
        function initLayerControls() {
            console.log('初始化图层控制');

            // 地质灾害点图层控制
            const disasterBtn = document.getElementById('disasterToolBtn');
            if (disasterBtn) {
                disasterBtn.addEventListener('click', function() {
                    console.log('地质灾害点按钮被点击');
                    this.classList.toggle('active');
                    const isActive = this.classList.contains('active');

                    if (disasterLayer) {
                        if (isActive) {
                            map.addLayer(disasterLayer);
                            console.log('显示地质灾害点图层');
                        } else {
                            map.removeLayer(disasterLayer);
                            console.log('隐藏地质灾害点图层');
                        }
                    }
                });
            }

            // 风险防范区图层控制
            const riskBtn = document.getElementById('riskToolBtn');
            if (riskBtn) {
                riskBtn.addEventListener('click', function() {
                    console.log('风险防范区按钮被点击');
                    this.classList.toggle('active');
                    const isActive = this.classList.contains('active');

                    if (riskLayer) {
                        if (isActive) {
                            map.addLayer(riskLayer);
                            console.log('显示风险防范区图层');
                        } else {
                            map.removeLayer(riskLayer);
                            console.log('隐藏风险防范区图层');
                        }
                    }
                });
            }
        }



        // 区县镇街数据已从外部JS文件引入

        // 预警信息管理函数
        function filterWarnings() {
            var selectedCounty = document.getElementById('warningCountySelect').value;
            var selectedTown = document.getElementById('warningTownSelect').value;

            filteredWarnings = warningData.filter(function(warning) {
                if (selectedTown !== '') {
                    return warning.towns.includes(selectedTown);
                } else if (selectedCounty !== '') {
                    var countyTownList = countyTowns[selectedCounty] || [];
                    return warning.towns.some(function(town) {
                        return countyTownList.includes(town);
                    });
                }
                return true;
            });

            currentPage = 1;
            renderWarningList();
            renderPagination();
        }

        function updateTownOptions() {
            var selectedCounty = document.getElementById('warningCountySelect').value;
            var townSelect = document.getElementById('warningTownSelect');

            townSelect.innerHTML = '<option value="">全部镇街</option>';

            if (selectedCounty && countyTowns[selectedCounty]) {
                countyTowns[selectedCounty].forEach(function(town) {
                    var option = document.createElement('option');
                    option.value = town;
                    option.textContent = town;
                    townSelect.appendChild(option);
                });
            }

            filterWarnings();
        }

        function renderWarningList() {
            var startIndex = (currentPage - 1) * pageSize;
            var endIndex = startIndex + pageSize;
            var pageWarnings = filteredWarnings.slice(startIndex, endIndex);

            var listContainer = document.getElementById('warningList');
            listContainer.innerHTML = '';

            pageWarnings.forEach(function(warning) {
                var warningItem = document.createElement('div');
                warningItem.className = 'warning-item';
                warningItem.onclick = function() { showWarningDetail(warning); };

                // 处理涉及镇街显示，最多显示前5个，超过则显示"等X个镇街"
                var townsDisplay = '';
                if (warning.towns.length <= 5) {
                    townsDisplay = warning.towns.join('、');
                } else {
                    townsDisplay = warning.towns.slice(0, 5).join('、') + '等' + warning.towns.length + '个镇街';
                }

                // 确定状态标识的样式类和文本
                var statusClass = warning.status === '生效' ? 'active' : 'inactive';
                var statusText = warning.status || '失效'; // 如果没有status字段，默认为失效

                warningItem.innerHTML =
                    '<div class="warning-header">' +
                        '<div class="warning-level warning-level-' + warning.level + '"></div>' +
                        '<div class="warning-title">' + warning.title +
                            '<span class="warning-status ' + statusClass + '">' + statusText + '</span>' +
                        '</div>' +
                    '</div>' +
                    '<div class="warning-meta">' +
                        '<span class="warning-time">' + warning.publishTime + '</span>' +
                        '<span class="warning-areas">' + warning.areas.length + '个区域</span>' +
                    '</div>' +
                    '<div class="warning-towns">' +
                        '<span class="warning-towns-label">涉及：</span>' + townsDisplay +
                    '</div>';

                listContainer.appendChild(warningItem);
            });
        }

        function renderPagination() {
            var totalPages = Math.ceil(filteredWarnings.length / pageSize);
            var paginationContainer = document.getElementById('warningPagination');
            paginationContainer.innerHTML = '';

            if (totalPages <= 1) return;

            // 上一页按钮
            var prevBtn = document.createElement('button');
            prevBtn.className = 'pagination-btn';
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = function() {
                if (currentPage > 1) {
                    currentPage--;
                    renderWarningList();
                    renderPagination();
                }
            };
            paginationContainer.appendChild(prevBtn);

            // 页码按钮
            for (var i = 1; i <= totalPages; i++) {
                var pageBtn = document.createElement('button');
                pageBtn.className = 'pagination-btn' + (i === currentPage ? ' active' : '');
                pageBtn.textContent = i;
                pageBtn.onclick = (function(page) {
                    return function() {
                        currentPage = page;
                        renderWarningList();
                        renderPagination();
                    };
                })(i);
                paginationContainer.appendChild(pageBtn);
            }

            // 下一页按钮
            var nextBtn = document.createElement('button');
            nextBtn.className = 'pagination-btn';
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderWarningList();
                    renderPagination();
                }
            };
            paginationContainer.appendChild(nextBtn);

            // 分页信息
            var infoSpan = document.createElement('span');
            infoSpan.className = 'pagination-info';
            infoSpan.textContent = '第' + currentPage + '页，共' + totalPages + '页';
            paginationContainer.appendChild(infoSpan);
        }

        function showWarningDetail(warning) {
            var modal = document.getElementById('warningModal');
            var modalTitle = document.getElementById('modalTitle');
            var modalContent = document.getElementById('modalContent');
            var modalTowns = document.getElementById('modalTowns');

            // 直接设置modalTitle的innerHTML，不依赖modalLevel元素
            var statusClass = warning.status === '生效' ? 'active' : 'inactive';
            var statusText = warning.status || '失效'; // 如果没有status字段，默认为失效

            modalTitle.innerHTML = '<div class="warning-level warning-level-' + warning.level + '"></div>' +
                                   warning.levelName + '预警详情' +
                                   '<span class="warning-status ' + statusClass + '">' + statusText + '</span>';
            modalContent.textContent = warning.content;

            modalTowns.innerHTML = '';
            warning.towns.forEach(function(town) {
                var townTag = document.createElement('span');
                townTag.className = 'town-tag';
                townTag.textContent = town;
                townTag.onclick = function() { panToTown(town); };
                modalTowns.appendChild(townTag);
            });

            modal.classList.add('show');
        }

        function hideWarningDetail() {
            var modal = document.getElementById('warningModal');
            modal.classList.remove('show');
        }

        // 拖动功能
        function makeDraggable(modal) {
            var header = modal.querySelector('.warning-modal-header');
            var isDragging = false;
            var currentX;
            var currentY;
            var initialX;
            var initialY;
            var xOffset = 0;
            var yOffset = 0;

            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            function dragStart(e) {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;

                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;
                }
            }

            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    xOffset = currentX;
                    yOffset = currentY;

                    modal.style.transform = 'translate(' + currentX + 'px, ' + currentY + 'px)';
                }
            }

            function dragEnd(e) {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
            }
        }

        // 侧边栏收起功能
        function initSidebarToggle() {
            var sidebar = document.getElementById('querySidebar');
            var toggleBtn = document.getElementById('sidebarToggle');
            var toggleIcon = toggleBtn.querySelector('i');
            var isCollapsed = false;

            toggleBtn.addEventListener('click', function() {
                isCollapsed = !isCollapsed;

                // 在动画开始前就触发地图重新计算，避免闪动
                if (map) {
                    // 立即触发一次
                    map.checkResize();

                    // 在动画过程中多次触发，确保平滑过渡
                    var resizeInterval = setInterval(function() {
                        map.checkResize();
                    }, 50); // 每50ms触发一次

                    // 动画完成后停止
                    setTimeout(function() {
                        clearInterval(resizeInterval);
                        map.checkResize(); // 最后再触发一次确保完全适应
                    }, 350);
                }

                if (isCollapsed) {
                    sidebar.classList.add('collapsed');
                    toggleIcon.className = 'fas fa-chevron-right';
                } else {
                    sidebar.classList.remove('collapsed');
                    toggleIcon.className = 'fas fa-chevron-left';
                }
            });
        }

        function panToTown(townName) {
            var coordinates = townCoordinates[townName];
            if (coordinates && map) {
                map.centerAndZoom(new T.LngLat(coordinates[0], coordinates[1]), 12);

                // 添加临时标记
                var marker = new T.Marker(new T.LngLat(coordinates[0], coordinates[1]));
                map.addOverLay(marker);

                // 3秒后移除标记
                setTimeout(function() {
                    map.removeOverLay(marker);
                }, 3000);
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initMap();

            // 初始化图层控制
            initLayerControls();

            // 显示当前生效的预警信息条
            showActiveWarningBanner();

            // 初始化图例按钮
            initLegendButton();

            // 初始化预警信息列表
            renderWarningList();
            renderPagination();

            // 绑定筛选事件
            document.getElementById('warningCountySelect').addEventListener('change', updateTownOptions);
            document.getElementById('warningTownSelect').addEventListener('change', filterWarnings);

            // 绑定对话框关闭事件
            document.getElementById('modalClose').addEventListener('click', hideWarningDetail);

            // 初始化拖动功能
            var modal = document.getElementById('warningModal');
            makeDraggable(modal);

            // 初始化侧边栏收起功能
            initSidebarToggle();
        };
    </script>
</body>
</html>
